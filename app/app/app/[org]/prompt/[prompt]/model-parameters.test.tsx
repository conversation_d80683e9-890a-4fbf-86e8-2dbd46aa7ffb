import { describe, it, expect } from "vitest";
import { defaultModelParamSettings } from "@braintrust/proxy/schema";

describe("Model Parameters - Verbosity Support", () => {
  it("should show verbosity for OpenAI format models", () => {
    const format = "openai";
    const hasVerbosity = defaultModelParamSettings[format]?.verbosity !== undefined;
    expect(hasVerbosity).toBe(true);
  });

  it("should not show verbosity for Anthropic format models", () => {
    const format = "anthropic";
    const hasVerbosity = defaultModelParamSettings[format]?.verbosity !== undefined;
    expect(hasVerbosity).toBe(false);
  });

  it("should not show verbosity for Google format models", () => {
    const format = "google";
    const hasVerbosity = defaultModelParamSettings[format]?.verbosity !== undefined;
    expect(hasVerbosity).toBe(false);
  });

  it("should not show verbosity for JS format models", () => {
    const format = "js";
    const hasVerbosity = defaultModelParamSettings[format]?.verbosity !== undefined;
    expect(hasVerbosity).toBe(false);
  });

  it("should not show verbosity for Window format models", () => {
    const format = "window";
    const hasVerbosity = defaultModelParamSettings[format]?.verbosity !== undefined;
    expect(hasVerbosity).toBe(false);
  });

  it("should not show verbosity for Converse format models", () => {
    const format = "converse";
    const hasVerbosity = defaultModelParamSettings[format]?.verbosity !== undefined;
    expect(hasVerbosity).toBe(false);
  });

  it("should have correct verbosity default value for OpenAI", () => {
    const format = "openai";
    const verbosityValue = defaultModelParamSettings[format]?.verbosity;
    expect(verbosityValue).toBe("medium");
  });

  it("should have undefined verbosity for non-OpenAI formats", () => {
    const nonOpenAIFormats = ["anthropic", "google", "converse"] as const;
    
    nonOpenAIFormats.forEach(format => {
      const verbosityValue = defaultModelParamSettings[format]?.verbosity;
      expect(verbosityValue).toBeUndefined();
    });
  });
});
