import { useFeatureFlags } from "#/lib/feature-flags";
import { Button } from "#/ui/button";
import { Checkbox } from "#/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { Input } from "#/ui/input";
import { UncontrolledNumericSlider } from "#/ui/numeric-slider";
import { Popover, PopoverContent, PopoverTrigger } from "#/ui/popover";
import { BasicTooltip } from "#/ui/tooltip";
import { cn } from "#/utils/classnames";
import { isEmpty } from "#/utils/object";
import {
  type ModelParams,
  type OpenAIModelParams,
} from "@braintrust/core/typespecs";
import {
  type ModelFormat,
  type ModelSpec,
  defaultModelParamSettings,
  modelProviderHasTools,
  sliderSpecs,
} from "@braintrust/proxy/schema";
import { Plus, Settings2 } from "lucide-react";
import Link from "next/link";
import React, { memo, useState } from "react";

export const PLAYGROUND_DEFAULT_MODEL_PARAMS: {
  [name in ModelFormat]: ModelParams;
} = {
  openai: {
    temperature: 0,
    use_cache: true,
  },
  anthropic: {
    temperature: 0,
    use_cache: true,
  },
  google: {
    temperature: 0,
    use_cache: true,
  },
  js: {},
  window: {},
  converse: {
    temperature: 0,
    use_cache: true,
  },
};

export const VERBOSITY_MODEL_PARAM_SETTINGS = {
  verbosity: {
    type: "dropdown" as const,
    options: ["low", "medium", "high"],
    defaultValue: "medium",
  },
} as const;

export const REASONING_MODEL_PARAM_SETTINGS = {
  reasoning_effort: {
    type: "dropdown" as const,
    options: ["minimal", "low", "medium", "high"],
    defaultValue: "medium",
  },
  reasoning_enabled: {
    type: "checkbox" as const,
    defaultValue: false,
  },
  reasoning_budget: {
    type: "slider" as const,
    min: 0,
    max: 32768,
    step: 1,
    defaultValue: 1024,
  },
} as const;

const REQUIRES_IS_THINKING = ["reasoning_enabled", "reasoning_budget"];

const ModelParameters = memo(function ModelParameters({
  modelSpec,
  params,
  saveParams,
  isReadOnly,
}: {
  modelSpec: ModelSpec;
  params?: ModelParams;
  saveParams: (params: ModelParams) => void;
  isReadOnly?: boolean;
}) {
  const [open, setOpen] = useState(false);
  const format = modelSpec.format;

  const isReasoning = !!modelSpec.reasoning || !!modelSpec.o1_like;
  const hasReasoningBudget = !!modelSpec.reasoning_budget;

  const {
    flags: { thinking: isThinkingEnabled },
  } = useFeatureFlags();

  const cacheDisabled =
    !params ||
    !("temperature" in params) ||
    isEmpty(params.temperature) ||
    (typeof params.temperature === "number" && params.temperature > 0);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <div>
          <BasicTooltip tooltipContent="Model parameters">
            <Button
              size="xs"
              className={cn(
                "h-8 flex-none rounded-l-none border border-l-0 text-primary-600",
                isReadOnly && "bg-primary-50",
              )}
              variant="ghost"
              isDropdown
              Icon={Settings2}
            >
              Params
            </Button>
          </BasicTooltip>
        </div>
      </PopoverTrigger>
      <PopoverContent
        align="end"
        onOpenAutoFocus={(e) => {
          e.preventDefault();
        }}
        className="flex flex-col gap-4 overflow-auto"
      >
        <div className="text-xs text-primary-500">Model parameters</div>
        {Object.entries(defaultModelParamSettings[format]).map(
          ([key, value]) => {
            if (sliderSpecs[key] === undefined) {
              return null;
            }
            const [min, max, step, required] = sliderSpecs[key];

            return (
              <div
                className="-m-2 rounded-md p-2 transition-colors hover:bg-primary-50"
                key={key}
              >
                <UncontrolledNumericSlider
                  disabled={isReadOnly}
                  title={underscoreToSentenceCase(key)}
                  min={min}
                  max={max}
                  step={step}
                  value={
                    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                    (params?.[key as keyof ModelParams] ??
                      value ??
                      undefined) as number | undefined
                  }
                  setValue={async (v) => {
                    if (v !== undefined) {
                      saveParams({ ...params, [key]: v });
                    } else {
                      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                      const { [key as keyof ModelParams]: _, ...rest } =
                        params ?? {};
                      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                      let newParams = { ...rest } as ModelParams;
                      const formatDefaults = defaultModelParamSettings[format];
                      if (required) {
                        newParams = {
                          ...newParams,
                          [key]:
                            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                            formatDefaults[key as keyof typeof formatDefaults],
                        };
                      }
                      saveParams(newParams);
                    }
                    return null;
                  }}
                  helpText={
                    isReasoning && key === "max_tokens" ? (
                      <span>
                        The maximum number of tokens to{" "}
                        <span className="font-medium">generate</span>, shared
                        between the prompt and completion. The exact limit
                        varies by model. One token is roughly 4 characters for
                        standard English text. For reasoning models (o-series,
                        claude-3-7-sonnet, etc.), this includes both reasoning
                        tokens and output tokens.
                      </span>
                    ) : (
                      modelParamHelpText[format]?.[key]
                    )
                  }
                />
              </div>
            );
          },
        )}
        <div className="-m-2 rounded-md p-2 transition-colors hover:bg-primary-50">
          <div className="flex items-center justify-between text-xs">
            <span className={cn(isReadOnly && "opacity-50")}>
              Stop sequences
            </span>
            <Button
              size="xs"
              Icon={Plus}
              disabled={isReadOnly}
              onClick={() => {
                // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                const currentStop = (params?.stop as string[]) || [];
                saveParams({
                  ...params,
                  stop: [...currentStop, ""],
                });
              }}
            />
          </div>
          {// eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
          (params?.stop as string[] | undefined)?.map((stop, index) => (
            <div key={index} className="mt-2 flex items-center gap-2">
              <Input
                placeholder="Enter stop sequence"
                className="h-8 w-full text-xs"
                value={stop}
                disabled={isReadOnly}
                onChange={(e) => {
                  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                  const newStop = [...((params?.stop as string[]) || [])];
                  newStop[index] = e.target.value;
                  saveParams({
                    ...params,
                    stop: newStop,
                  });
                }}
              />
              <Button
                size="xs"
                variant="ghost"
                disabled={isReadOnly}
                onClick={() => {
                  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                  const newStop = [...((params?.stop as string[]) || [])];
                  newStop.splice(index, 1);
                  saveParams({
                    ...params,
                    stop: newStop.length > 0 ? newStop : undefined,
                  });
                }}
              >
                ✕
              </Button>
            </div>
          ))}
        </div>
        {modelProviderHasTools[format] && (
          <ToolSelector
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            value={params && (params as OpenAIModelParams).tool_choice}
            isReadOnly={isReadOnly}
            setValue={(v) => {
              if (v !== undefined && v !== "none") {
                saveParams({ ...params, tool_choice: v });
              } else {
                // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                const { tool_choice: _, ...rest } = (params ??
                  {}) as OpenAIModelParams;
                // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                saveParams({ ...rest } as ModelParams);
              }
            }}
          />
        )}
        {isReasoning &&
          Object.entries(REASONING_MODEL_PARAM_SETTINGS).map(
            ([key, setting], i) => {
              if (key === "reasoning_effort" && hasReasoningBudget) {
                return null;
              }

              if (
                (key === "reasoning_enabled" || key === "reasoning_budget") &&
                !hasReasoningBudget
              ) {
                return null;
              }

              if (key === "reasoning_budget" && !params?.reasoning_enabled) {
                return null;
              }

              if (REQUIRES_IS_THINKING.includes(key) && !isThinkingEnabled) {
                return null;
              }

              const title = underscoreToSentenceCase(key);

              return (
                <div key={i}>
                  {setting.type === "dropdown" && (
                    <BasicTooltip
                      side="left"
                      sideOffset={16}
                      tooltipContent={modelParamHelpText.openai[key]}
                    >
                      <div
                        className={cn(
                          "-m-2 flex items-center justify-between rounded-md p-2 text-xs transition-colors hover:bg-primary-50",
                          isReadOnly && "opacity-50",
                        )}
                      >
                        {title}

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button size="xs" isDropdown>
                              {underscoreToSentenceCase(
                                params?.[key]?.toString() ??
                                  setting.defaultValue,
                              )}
                            </Button>
                          </DropdownMenuTrigger>

                          <DropdownMenuContent>
                            {setting.options.map((value) => (
                              <DropdownMenuCheckboxItem
                                checked={params?.[key] === value}
                                key={value}
                                disabled={isReadOnly}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  saveParams({
                                    ...params,
                                    [key]: value,
                                  });
                                }}
                              >
                                {underscoreToSentenceCase(value)}
                              </DropdownMenuCheckboxItem>
                            ))}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </BasicTooltip>
                  )}
                  {setting.type === "checkbox" && (
                    <div
                      className={cn(
                        "text-xs flex items-center justify-between rounded-md p-2 transition-colors hover:bg-primary-50 -m-2",
                        isReadOnly && "opacity-50",
                      )}
                    >
                      <div>{title}</div>
                      <Checkbox
                        checked={!!params?.[key]}
                        disabled={isReadOnly}
                        onChange={(e) => {
                          saveParams({
                            ...params,
                            [key]: e.target.checked,
                          });
                        }}
                      />
                    </div>
                  )}
                  {setting.type === "slider" && (
                    <UncontrolledNumericSlider
                      title={title}
                      min={setting.min}
                      max={setting.max}
                      step={setting.step}
                      disabled={isReadOnly}
                      value={
                        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                        (params?.[key] ?? setting.defaultValue) as number
                      }
                      setValue={async (v) => {
                        saveParams({
                          ...params,
                          [key]: v,
                        });
                        return null;
                      }}
                    />
                  )}
                </div>
              );
            },
          )}
        {defaultModelParamSettings[format]?.verbosity !== undefined && (
          <BasicTooltip
            side="left"
            sideOffset={16}
            tooltipContent="Controls the level of detail in the model's responses"
          >
            <div
              className={cn(
                "-m-2 flex items-center justify-between rounded-md p-2 text-xs transition-colors hover:bg-primary-50",
                isReadOnly && "opacity-50",
              )}
            >
              Verbosity
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button size="xs" isDropdown>
                    {underscoreToSentenceCase(
                      params?.verbosity?.toString() ??
                        VERBOSITY_MODEL_PARAM_SETTINGS.verbosity.defaultValue,
                    )}
                  </Button>
                </DropdownMenuTrigger>

                <DropdownMenuContent>
                  {VERBOSITY_MODEL_PARAM_SETTINGS.verbosity.options.map(
                    (value) => (
                      <DropdownMenuCheckboxItem
                        checked={params?.verbosity === value}
                        key={value}
                        disabled={isReadOnly}
                        onClick={(e) => {
                          e.stopPropagation();
                          saveParams({
                            ...params,
                            verbosity: value,
                          });
                        }}
                      >
                        {underscoreToSentenceCase(value)}
                      </DropdownMenuCheckboxItem>
                    ),
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </BasicTooltip>
        )}
        <BasicTooltip
          side="left"
          sideOffset={16}
          tooltipContent={
            cacheDisabled
              ? "Set temperature to 0 to enable caching."
              : "Cache completions. This is only available when temperature is set to 0."
          }
        >
          <div
            className={cn(
              "text-xs flex items-center justify-between rounded-md p-2 transition-colors hover:bg-primary-50 -m-2",
              (isReadOnly || cacheDisabled) && "opacity-50",
            )}
          >
            <div>Use cache</div>
            <Checkbox
              disabled={isReadOnly || cacheDisabled}
              checked={
                !!(
                  params &&
                  "temperature" in params &&
                  params.temperature === 0 &&
                  params.use_cache !== false // if temperature === 0 and use_cache is undefined, we want to default to true
                )
              }
              onChange={(e) => {
                saveParams({
                  ...params,
                  use_cache:
                    params &&
                    "temperature" in params &&
                    params.temperature === 0 &&
                    e.target.checked,
                });
              }}
            />
          </div>
        </BasicTooltip>
      </PopoverContent>
    </Popover>
  );
});

ModelParameters.displayName = "ModelParameters";
export default ModelParameters;

export const toolsChoiceDescription = (
  <>
    Controls which (if any) function is called by the model.{" "}
    <code className="font-semibold">none</code> means the model will not call a
    function and instead generates a message.{" "}
    <code className="font-semibold">auto</code> means the model can pick between
    generating a message or calling a function. Specifying a particular function
    via{" "}
    <code className="font-semibold">{`{"type": "function", "function": {"name": "my_function"}}`}</code>{" "}
    forces the model to call that function.
    <span className="flex flex-col gap-2 pt-2">
      <span className="block">
        <code className="font-semibold">none</code> is the default when no
        functions are present.
      </span>
      <span className="block">
        <code className="font-semibold">auto</code> is the default if functions
        are present.
      </span>
    </span>
  </>
);

function ToolSelector({
  value,
  setValue,
  isReadOnly,
}: {
  value?: OpenAIModelParams["tool_choice"];
  setValue: (v: OpenAIModelParams["tool_choice"] | undefined) => void;
  isReadOnly?: boolean;
}) {
  const showCustom = typeof value === "object" && value !== null;
  const [functionName, setFunctionName] = useState(
    showCustom ? value.function?.name || "" : "",
  );
  return (
    <BasicTooltip
      side="left"
      sideOffset={16}
      tooltipContent={toolsChoiceDescription}
    >
      <div className="-m-2 rounded-md p-2 transition-colors hover:bg-primary-50">
        <div className="flex items-center justify-between text-xs">
          <span className={cn(isReadOnly && "opacity-50")}>Tool choice</span>
          <ToolChoiceDropdownMenu
            showCustom={showCustom}
            value={value}
            setValue={setValue}
            isReadOnly={isReadOnly}
          />
        </div>

        {showCustom && (
          <div className="mt-2">
            <Input
              placeholder="Enter function name"
              className="h-8 w-full font-mono text-xs disabled:cursor-default"
              value={functionName}
              disabled={isReadOnly}
              onChange={(e) => setFunctionName(e.target.value)}
              onBlur={(e) => {
                setValue(
                  e.target.value
                    ? { type: "function", function: { name: e.target.value } }
                    : undefined,
                );
              }}
            />
          </div>
        )}
      </div>
    </BasicTooltip>
  );
}

export const ToolChoiceDropdownMenu = ({
  className,
  showCustom,
  value,
  setValue,
  align = "end",
  isReadOnly,
}: {
  className?: string;
  showCustom: boolean;
  value: OpenAIModelParams["tool_choice"];
  setValue: (v: OpenAIModelParams["tool_choice"] | undefined) => void;
  align?: "start" | "end";
  isReadOnly?: boolean;
}) => (
  <DropdownMenu>
    <DropdownMenuTrigger asChild>
      <Button
        size="xs"
        isDropdown={!isReadOnly}
        className={className}
        disabled={isReadOnly}
      >
        {showCustom ? (
          "Function name"
        ) : typeof value === "string" ? (
          <span className="capitalize">{value}</span>
        ) : (
          "None"
        )}
      </Button>
    </DropdownMenuTrigger>

    <DropdownMenuContent align={align}>
      <DropdownMenuCheckboxItem
        checked={value === "none" || (value === undefined && !showCustom)}
        onClick={(e) => {
          e.stopPropagation();
          setValue(value === "none" ? undefined : "none");
        }}
      >
        None
      </DropdownMenuCheckboxItem>
      <DropdownMenuCheckboxItem
        checked={value === "auto" && !showCustom}
        onClick={(e) => {
          e.stopPropagation();
          setValue(value === "auto" ? undefined : "auto");
        }}
      >
        Auto
      </DropdownMenuCheckboxItem>
      <DropdownMenuCheckboxItem
        checked={value === "required" && !showCustom}
        onClick={(e) => {
          e.stopPropagation();
          setValue(value === "required" ? undefined : "required");
        }}
      >
        Required
      </DropdownMenuCheckboxItem>
      <DropdownMenuCheckboxItem
        checked={showCustom}
        onClick={(e) => {
          e.stopPropagation();
          setValue(
            showCustom
              ? undefined
              : { type: "function", function: { name: "" } },
          );
        }}
      >
        Function name
      </DropdownMenuCheckboxItem>
    </DropdownMenuContent>
  </DropdownMenu>
);

function underscoreToSentenceCase(s: string) {
  return s
    .split("_")
    .map((w, i) => (i === 0 ? w[0].toUpperCase() + w.slice(1) : w))
    .join(" ");
}

const modelParamHelpText: {
  [name in ModelFormat]: { [name: string]: React.ReactNode };
} = {
  openai: {
    temperature: (
      <span>
        Controls randomness: Lowering results in less random completions. As the
        temperature approaches zero, the model will become deterministic and
        repetitive.
      </span>
    ),
    max_tokens: (
      <span>
        The maximum number of tokens to{" "}
        <span className="font-medium">generate</span>, shared between the prompt
        and completion. The exact limit varies by model. One token is roughly 4
        characters for standard English text.
      </span>
    ),
    top_p: (
      <span>
        Controls diversity via nucleus sampling: 0.5 means half of all
        likelihood-weighted options are considered.
      </span>
    ),
    frequency_penalty: (
      <span>
        How much to penalize new tokens based on their existing frequency in the
        text so far. Decreases the model&apos;s likelihood to repeat the same
        line verbatim.
      </span>
    ),
    presence_penalty: (
      <span>
        How much to penalize new tokens based on whether they appear in the text
        so far. Increases the model&apos;s likelihood to talk about new topics.
      </span>
    ),
    reasoning_effort: (
      <span>
        Constrains effort on reasoning for reasoning models. Currently supported
        values are low, medium, and high. Reducing reasoning effort can result
        in faster responses and fewer tokens used on reasoning in a response.
        <span className="block pt-1 text-primary-500">
          This parameter is only available for o-series models.
        </span>
      </span>
    ),
    reasoning_enabled: <span>Enables reasoning for reasoning models.</span>,
    reasoning_budget: (
      <span>
        Constrains the budget for reasoning for reasoning models. A model may
        have a minimum budget required. The budget must not exceed max tokens.
      </span>
    ),
  },
  anthropic: {
    temperature: (
      <span>
        Amount of randomness injected into the response. Defaults to 1. Ranges
        from 0 to 1. Use temp closer to 0 for analytical / multiple choice, and
        closer to 1 for creative and generative tasks.
      </span>
    ),
    max_tokens: (
      <span>
        The maximum number of tokens to generate before stopping. Note that our
        models may stop before reaching this maximum. This parameter only
        specifies the absolute maximum number of tokens to generate.
      </span>
    ),
    top_p: (
      <span>
        Use nucleus sampling. In nucleus sampling, we compute the cumulative
        distribution over all the options for each subsequent token in
        decreasing probability order and cut it off once it reaches a particular
        probability specified by top_p. You should either alter temperature or
        top_p, but not both.
      </span>
    ),
    top_k: (
      <span>
        Only sample from the top K options for each subsequent token. Used to
        remove {' "long tail" '} low probability responses.{" "}
        <Link
          href="https://towardsdatascience.com/how-to-sample-from-language-models-682bceb97277"
          target="_blank"
          className="underline"
          scroll={false}
        >
          Learn more
        </Link>
        .
      </span>
    ),
  },
  google: {
    temperature: (
      <span>
        The temperature controls the degree of randomness in token selection.
        The temperature is used for sampling during response generation, which
        occurs when topP and topK are applied. Lower temperatures are good for
        prompts that require a more deterministic or less open-ended response,
        while higher temperatures can lead to more diverse or creative results.
        A temperature of 0 is deterministic, meaning that the highest
        probability response is always selected.
      </span>
    ),
    maxOutputTokens: (
      <span>
        Specifies the maximum number of tokens that can be generated in the
        response. A token is approximately four characters. 100 tokens
        correspond to roughly 60-80 words.
      </span>
    ),
    topP: (
      <span>
        The topP parameter changes how the model selects tokens for output.
        Tokens are selected from the most to least probable until the sum of
        their probabilities equals the topP value. For example, if tokens A, B,
        and C have a probability of 0.3, 0.2, and 0.1 and the topP value is 0.5,
        then the model will select either A or B as the next token by using the
        temperature and exclude C as a candidate. The default topP value is
        0.95.
      </span>
    ),
    topK: (
      <span>
        The topK parameter changes how the model selects tokens for output. A
        topK of 1 means the selected token is the most probable among all the
        tokens in the model&apos;s vocabulary (also called greedy decoding),
        while a topK of 3 means that the next token is selected from among the 3
        most probable using the temperature. For each token selection step, the
        topK tokens with the highest probabilities are sampled. Tokens are then
        further filtered based on topP with the final token selected using
        temperature sampling.
      </span>
    ),
  },
  js: {},
  window: {
    temperature: (
      <span>
        The temperature controls the degree of randomness in token selection.
        The temperature is used for sampling during response generation, which
        occurs when topP and topK are applied. Lower temperatures are good for
        prompts that require a more deterministic or less open-ended response,
        while higher temperatures can lead to more diverse or creative results.
        A temperature of 0 is deterministic, meaning that the highest
        probability response is always selected.
      </span>
    ),
    topK: (
      <span>
        The topK parameter changes how the model selects tokens for output. A
        topK of 1 means the selected token is the most probable among all the
        tokens in the model&apos;s vocabulary (also called greedy decoding),
        while a topK of 3 means that the next token is selected from among the 3
        most probable using the temperature. For each token selection step, the
        topK tokens with the highest probabilities are sampled. Tokens are then
        further filtered based on topP with the final token selected using
        temperature sampling.
      </span>
    ),
  },
  converse: {
    temperature: (
      <span>
        Amount of randomness injected into the response. Defaults to 1. Ranges
        from 0 to 1. Use temp closer to 0 for analytical / multiple choice, and
        closer to 1 for creative and generative tasks.
      </span>
    ),
    max_tokens: (
      <span>
        The maximum number of tokens to generate before stopping. Note that our
        models may stop before reaching this maximum. This parameter only
        specifies the absolute maximum number of tokens to generate.
      </span>
    ),
    top_p: (
      <span>
        Use nucleus sampling. In nucleus sampling, we compute the cumulative
        distribution over all the options for each subsequent token in
        decreasing probability order and cut it off once it reaches a particular
        probability specified by top_p. You should either alter temperature or
        top_p, but not both.
      </span>
    ),
  },
};
